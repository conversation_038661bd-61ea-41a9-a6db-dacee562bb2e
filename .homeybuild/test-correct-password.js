#!/usr/bin/env node

/**
 * Test with the correct password: pcs2ass2ADM
 */

const axios = require('axios');
const CryptoJS = require('crypto-js');

// Configuration with correct password
const ROUTER_CONFIG = {
  ip: '*************',
  password: 'pcs2ass2ADM'
};

async function testCorrectPassword() {
  console.log('=== Testing with Correct Password ===\n');
  
  const client = axios.create({
    baseURL: `http://${ROUTER_CONFIG.ip}`,
    timeout: 10000,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
    withCredentials: true,
  });

  const correctKey = 'RjYkhwzx$2018!';

  try {
    // Test 1: API login with correct password
    console.log('Test 1: API login with correct password...');
    const encryptedPassword = CryptoJS.AES.encrypt(ROUTER_CONFIG.password, correctKey).toString();
    
    const loginData = {
      method: "login",
      params: {
        password: encryptedPassword,
        username: 'admin',
        time: Math.floor(Date.now() / 1000).toString(),
        encry: true,
        limit: false
      }
    };

    const apiResponse = await client.post('/cgi-bin/luci/api/auth', loginData);
    console.log('API login response:', JSON.stringify(apiResponse.data, null, 2));
    console.log('API login headers:', JSON.stringify(apiResponse.headers, null, 2));

    // Check main page after API login
    const mainPageAfterApi = await client.get('/cgi-bin/luci/');
    if (!mainPageAfterApi.data.includes('loginPass')) {
      console.log('🎉 API login succeeded!');
      
      const fs = require('fs');
      fs.writeFileSync('api-login-success.html', mainPageAfterApi.data);
      console.log('Saved API login success page');
      
      // Look for session token
      const stokMatch = mainPageAfterApi.data.match(/stok=([a-zA-Z0-9]+)/);
      if (stokMatch) {
        console.log(`Found session token: ${stokMatch[1]}`);
        return stokMatch[1]; // Return the token for further testing
      }
    } else {
      console.log('❌ API login failed - still on login page');
    }

    // Test 2: Form login with correct password
    console.log('\nTest 2: Form login with correct password...');
    
    const formData = new URLSearchParams();
    formData.append('loginPass', ROUTER_CONFIG.password);
    
    const formResponse = await client.post('/cgi-bin/luci/', formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Referer': `http://${ROUTER_CONFIG.ip}/cgi-bin/luci/`
      }
    });
    
    console.log('Form login response status:', formResponse.status);
    console.log('Form login response headers:', JSON.stringify(formResponse.headers, null, 2));
    
    if (!formResponse.data.includes('loginPass')) {
      console.log('🎉 Form login succeeded!');
      
      const fs = require('fs');
      fs.writeFileSync('form-login-success.html', formResponse.data);
      console.log('Saved form login success page');
      
      // Look for session token
      const stokMatch = formResponse.data.match(/stok=([a-zA-Z0-9]+)/);
      if (stokMatch) {
        console.log(`Found session token: ${stokMatch[1]}`);
        
        // Test API with this token
        console.log('\nTesting API access with session token...');
        try {
          const apiTestResponse = await client.post(`/cgi-bin/luci/;stok=${stokMatch[1]}/api/system`, {
            method: 'get_system_info',
            params: {}
          });
          console.log('API test response:', JSON.stringify(apiTestResponse.data, null, 2));
        } catch (apiError) {
          console.log('API test failed:', apiError.message);
          
          // Try different API endpoints
          const endpoints = [
            `/cgi-bin/luci/;stok=${stokMatch[1]}/api/network`,
            `/cgi-bin/luci/;stok=${stokMatch[1]}/api/wireless`,
            `/cgi-bin/luci/;stok=${stokMatch[1]}/api/status`
          ];
          
          for (const endpoint of endpoints) {
            try {
              const response = await client.get(endpoint);
              console.log(`${endpoint}: Success - ${JSON.stringify(response.data)}`);
            } catch (error) {
              console.log(`${endpoint}: ${error.response?.status || 'Error'} - ${error.message}`);
            }
          }
        }
        
        return stokMatch[1];
      }
    } else {
      console.log('❌ Form login failed - still on login page');
    }

    console.log('\n🎉 Password test completed!');
    return null;

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
    return null;
  }
}

// Run the test
testCorrectPassword().then(token => {
  if (token) {
    console.log(`\n✅ Successfully authenticated! Session token: ${token}`);
  } else {
    console.log('\n❌ Authentication failed');
  }
}).catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
