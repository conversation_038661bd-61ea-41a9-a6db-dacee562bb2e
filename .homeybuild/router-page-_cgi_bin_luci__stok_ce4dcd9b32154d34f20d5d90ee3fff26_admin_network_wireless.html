<!DOCTYPE html>
<html>
<!--[if lte IE 8]><script>
    window.location.href = '/error.html?fromsysauth' + Math.random()
</script><![endif]-->


<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <title>Ruijie</title>
  <link rel="shortcut icon" href="/luci-static/eweb-ehr/favicon.ico">
  <script type="text/javascript">
    if ('' === 'true' && window.location.protocol !== 'https:') {
      var time = (new Date().getTime() / 1000).toFixed(0);
      window.location.href = "https://" + window.location.host + "/cgi-bin/luci/?stamp=" + time;
    }
    // 如果if条件注释不生效，再次通过js判断浏览器类型
    window.LANG_KEY = "__APP_LANG__";
    (function (window) { // isLowerIEVersion
      var userAgent = navigator.userAgent;
      var isIE = userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1 && !(userAgent
        .indexOf("Opera") > -1);
      if (isIE && parseFloat(userAgent.match(/MSIE\s+(\d+\.\d+)/)[1] || 0) < 9) {
        window.location.href = '/error.html?fromhome' + Math.random()
      }
      var storageLang = 'en'
      document.cookie = window.LANG_KEY + '=' + storageLang + ';path=/';
    })(window);
  </script>
  <style type="text/css">
    /* 通用样式 */
    * {
      box-sizing: border-box
    }

    .g-none {
      display: none;
    }

    .fl {
      float: left;
    }

    .fr {
      float: right;
    }

    .clearfix:after {
      content: "";
      height: 0;
      visibility: hidden;
      display: block;
      clear: both;
    }

    .clearfix {
      zoom: 1;
    }

    .tc {
      text-align: center;
    }

    .tl {
      text-align: left;
    }

    body,
    body a,
    body a:hover {
      color: #2EBA97;
    }

    html {
      height: 100%;
    }

    /* 登录页和首页 公用页脚样式 */
    .footer {
      font-weight: 300;
      font-size: 14px;
      font-family: Arial, '微软雅黑', "新宋体";
      color: #8290a1
    }

    .footer-logo {
      background: url("/luci-static/eweb-ehr/static/image/ehr/logo.png") left bottom no-repeat;
      padding: 1px 27px;
      height: 18px;
      display: inline-block;
      vertical-align: sub;
    }

    .footer a {
      cursor: pointer;
      text-decoration: none;
      outline: none;
      padding: 0 9px 0 5px;
      position: relative;
    }

    .footer label {
      padding: 0 9px 0 5px;
    }

    .footer span {
      padding: 0 3px;
    }

    .border-r {
      border-right: 1px solid #8290a1;
    }

    /* ODM 页脚样式 */
    .footer-index-wrap {
      position: relative;
      height: 10%;
    }

    /* 页脚固定在下方 */
    .footer-index {
      position: absolute;
      bottom: -10px;
      width: 100%;
    }

    .footer-main {
      float: right;
      padding: 5px 0;
      text-align: right;
    }

    /* 登录页样式 */
    .body-index {
      height: 100%;
      min-width: 320px;
      min-height: 480px;
      margin: 0;
    }

    .lg-wrap {
      height: 100%
    }

    .lg-top {
      height: 50%;
      width: 100%;
      padding-top: 110px;
    }

    .lg-font {
      font-family: "PingHei", "PingFang SC", "\5FAE\8F6F\96C5\9ED1", "Helvetica", "Arial", sans-serif;
      color: #fff;
    }

    .header-logo {
      width: 100%;
      /* height: 24%; */
      min-height: 70px;
      position: relative;
      text-align: center;
      overflow: hidden;
      margin-bottom: 10px;
    }

    .header-logo i {
      display: inline-block;
      width: 520px;
      height: 70px;
      background-repeat: no-repeat;
      background-size: cover;
      background-position: center;
    }

    .lg-subhead {
      font-size: 20px;
      font-family: Alibaba PuHuiTi;
      color: rgba(255, 255, 255, 1);
    }

    .lg-service {
      background: rgba(255, 255, 255, 0.32);
      border-radius: 3px;
      padding: 3px 8px;
      margin: 5px 0;
    }

    .lg-service a,
    .lg-service a:hover {
      color: #fff;
      text-decoration: none;
    }

    .lg-middle {
      position: relative;
      width: 360px;
      height: 39%;
      margin: -22px auto 0
    }

    .lg-form {
      position: relative;
      height: 44px;
    }

    .lg-form input {
      position: absolute;
      border: 1px solid #2EBA97;
      padding: 12px 40px 12px 20px;
      border-radius: 3px;
      font-size: 16px;
      width: 100%;
      color: #949ea7
    }

    .lg-form input:focus {
      outline: 0;
      -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(1, 102, 255, .6);
      box-shadow: 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(1, 102, 255, .6);
      color: #222a31
    }

    .lg-form .icon {
      position: absolute;
      top: 15px;
      right: 10px;
      width: 24px;
      z-index: 999;
      cursor: pointer;
    }

    .mb20 {
      margin-bottom: 20px;
    }

    .lg-form a {
      text-decoration: none;
    }

    .lg-btn-submit {
      height: 40px;
      font-size: 18px;
      cursor: pointer;
      background-color: #2EBA97;
      border: none
    }

    .lg-btn-submit.disable {
      background-color: #BFBFBF;
      cursor: no-drop;
    }

    .lg-tip {
      display: none;
      position: absolute;
      top: -150px;
      left: 25px;
      line-height: 25px;
      width: 350px;
      background-color: #fff;
      border: 2px solid #2EBA97;
      padding: 1px;
    }

    .lg-tip p {
      padding: 0px 20px 8px;
      color: #666;
    }

    .lg-tip h2 {
      font-size: 15px;
      line-height: 32px;
      border-bottom: 1px solid #d0d0d0;
      color: #666;
      padding: 4px 4px 4px 10px;
      margin: 0;
    }

    .lg-tip h2 a {
      font-size: 28px;
      text-decoration: none;
      float: right;
      font-weight: normal;
      line-height: 24px;
    }

    .lg-tip h2 a:hover {
      background: #2EBA97;
      color: #fff;
    }

    .lg-auth-tip {
      color: #d43f3a;
      margin-top: 15px;
      font-size: 16px;
    }

    /* 首页页脚 手机/PC 响应式样式 */
    .footer-mobile {
      display: none;
    }

    .header-logo .auto-size {
      position: relative;
      width: 240px;
    }

    @media screen and (max-width:520px) {
      .header-logo .auto-size {
        position: relative;
        width: 330px;
      }

      .lg-middle {
        width: 300px;
      }

      .lg-form input {
        width: 300px
      }

      .lg-tip {
        width: 250px
      }

      .footer-pc {
        display: none;
      }

      .footer-mobile {
        display: block;
      }
    }

    @media screen and (max-width:400px) {
      .header-logo .auto-size {
        position: relative;
        width: 300px;
      }

      .lg-subhead {
        font-size: 18px;
      }

      .lg-middle {
        width: 260px;
      }

      .lg-form input {
        width: 260px
      }

      .lg-tip {
        width: 215px
      }
    }

    .input-fail {
      border: 1px solid #DE321F !important;
      background: #fefef2;
    }

    .error-tip {
      color: #DE321F;
    }

    /* 输入框错误抖动 */
    @keyframes move {
      from {
        left: 0px;
      }

      to {
        left: -5px;
      }
    }

    @-webkit-keyframes move {
      from {
        left: 0px;
      }

      to {
        left: -5px;
      }
    }

    /*.lg-form input {position: absolute;} /* 配置move的left实现移动 */
    .shake {
      animation: move 0.05s ease-in-out 0s 12 alternate;
      -moz-animation: move 0.05s ease-in-out 0s 12 alternate;
      /* Firefox: */
      -webkit-animation: move 0.05s ease-in-out 0s 12 alternate;
      /* Safari 和 Chrome: */
      -o-animation: move 0.05s ease-in-out 0s 12 alternate;
      /* Opera: */
    }

    .lg-top {
      background: linear-gradient(280deg, #79d4b7 0%, #1E947A 100%);
      background-color: #2EBA97;
    }

    .lg-form input,
    .lg-btn-submit {
      border-radius: 4px;
      width: 100%;
    }

    .lang {
      padding: 3px 5px;
    }
  </style>
</head>

<body id="body_index" class="body-index">
  <div class="lg-wrap">
    <div class="lg-top tc">
      <div class="header-logo">
        <img src="/luci-static/eweb-ehr/static/image/logo-rj.png" class="auto-size" />
      </div>
      <div class="lg-font lg-subhead">Hi,
        X32-PRO
      </div>
    </div>
    <div class="lg-middle">
      <div class="g-none">
        <input name="loginName">
        <input name="loginPass" type="password">
      </div>
      
      <div class="lg-form">
        <input autocomplete="new-password" name="loginPass" id="password" type="password"
          placeholder="Password">
        <img class="icon" id="icon-pwd" onclick="showPwd()" src="/luci-static/eweb-ehr/static/image/hide.png">
      </div>
      <div id="errorTimes" class="error-tip mb20">
        <!-- 密码已错误 3 次，请 %d 秒后重新再登录 -->
      </div>
      <div class="tc">
        <button class="lg-font lg-btn-submit tc" type="submit" id="login">Log In</button>
        <div class="lg-auth-tip" id="lg_auth_tip"></div>
      </div>
      <div class="lg-form clearfix tc">
        <div class="fl">
          <a href="javascript:" id="forgot_a">Forgot Password?</a>
          <div id="forgot_password" class="lg-tip">
            <h2 class="lg-font clearfix">
              <span class="fl">What to do if you forgot the password?</span>
              <a href="javascript:" id="forgot_close">x</a>
            </h2>
            <p class="tl">
              
              Please enter the default management password on the label of the device. If the password is still incorrect, press the RESET button on the panel for 10 seconds to restore the device to factory settings after the device is powered on. The password will be restored to the default management password.
              
            </p>
          </div>
        </div>
        
        <div class="fr">
          <select id="lang" class="lang" onchange="Login.switchLang(this)">
            
            <option value="en" selected>English</option>
            
            <option value="zh_cn" >中文</option>
            
            <option value="zh_ft" >繁體</option>
            
            <option value="th" >ไทย</option>
            
            <option value="vi" >Việt Nam</option>
            
            <option value="es" >español</option>
            
            <option value="ru" >русский</option>
            
            <option value="tr" >Türkiye</option>
            
            <option value="id" >Indonesia</option>
            
            <option value="ar" >العربية</option>
            
          </select>
        </div>
        
      </div>
      <div id="lowTip" style="color: #f47f3e;font-size: 14px;line-height: 20px;padding-top: 20px;display: none;">
        Your browser version is low. We suggest you to use
        <strong>Chrome</strong> Browser</div>
    </div>
    <div class="footer-index-wrap tc">
      <div class="footer footer-index footer-pc">
        <div style="margin-top: 5px; display: inline-block">
          <span>Google Chrome and IE browser 9, 10 or 11 are supported.</span>&nbsp;
          <span class="footer-year"></span>
        </div>
        <br>
        <div class="footer-logo"></div>
        
      </div>
      <div class="footer footer-index footer-mobile tc">
        <div class="footer-logo"></div>
        <span class="footer-year"></span>
      </div>
    </div>
  </div>
  <script src="/luci-static/eweb-ehr/static/aes.js"></script>
  <script type="text/javascript">
    var INITPATH = '';
    var url = "/cgi-bin/luci/api/auth";
    var title = 'Ruijie Networks-EWEB';
    document.title = title;
    var k = GibberishAES.dec('U2FsdGVkX19ecPYL/ZAlcSG29wb6ivqD9YjEM30k1h8=', 'eweb').replace(/\s+/g, '')
    var Login = {
      init: function () {
        if (location.search.indexOf('pass=') != -1) {
          var data = this._getQuery();
          var encPd = data.type === "plaintext" ?
            GibberishAES.enc(data.pass, k).replace(/\s+/g, '') :
            data.pass
          this.byId('password').value = encPd || '';
          INITPATH = data.initpath || INITPATH || ''
          this.onLogin(true);
        }
        this.initView();
        this.initEvent();
        this.showPwd();
      },
      switchLang: function (dom) {
        document.cookie = window.LANG_KEY + '=' + dom.value + ';path=/';
        setTimeout(function () {
          window.location.reload();
        }, 300);
      },
      initView: function () {
        this.byId("password").focus();
        var _years = 'Copyright©2000-%d Ruijie Networks Co., Ltd.'.replace('%d', Math.max(2020, new Date().getFullYear()))
        var _doms = document.getElementsByClassName("footer-year")
        for (var i = 0; i < _doms.length; i++) {
          _doms[i].innerHTML = _years
        }
      },
      _getQuery: function () {
        var data = {}
        try {
          location.search.substr(1).split("&").forEach(function (d, i) {
            var temp = d.split(/\s*=\s*/);
            data[decodeURIComponent(temp[0])] = decodeURIComponent(temp[1]);
          })
        } catch (error) { }
        return data;
      },
      byId: function (id) {
        return document.getElementById(id);
      },
      onPost: function (opt) {
        try {
          function createAjax() {
            var request = false;
            if (window.ActiveXObject) {
              var versions = ['Microsoft.XMLHTTP', 'MSXML.XMLHTTP', 'Msxml2.XMLHTTP.7.0',
                'Msxml2.XMLHTTP.6.0', 'Msxml2.XMLHTTP.5.0', 'Msxml2.XMLHTTP.4.0',
                'MSXML2.XMLHTTP.3.0', 'MSXML2.XMLHTTP'
              ];
              for (var i = 0; i < versions.length; i++) {
                try {
                  request = new ActiveXObject(versions[i]);
                  if (request) {
                    return request;
                  }
                } catch (e) {
                  request = false;
                }
              }
            } else if (window.XMLHttpRequest) {
              request = new XMLHttpRequest();
              if (request.overrideMimeType) {
                request.overrideMimeType("text/xml");
              }
            }
            return request;
          }
          var self = this,
            data = opt.data,
            url = opt.url
          var xhr = createAjax();
          xhr.open("POST", url)
          xhr.setRequestHeader("Content-type", "application/json");
          xhr.send(JSON.stringify(data));
          xhr.onreadystatechange = function () {
            if (xhr.readyState == 4) {
              if (xhr.status == 200) {
                var res = xhr.responseText;
                if (typeof xhr.responseText === "object")
                  res = res;
                if (typeof xhr.responseText === "string")
                  res = JSON.parse(res.trim());

                opt.success.call(self, res.data);
              } else {
                opt.err.call(self, xhr);
              }
            }
          };
        } catch (e) {
          alert("Google Chrome is recommended.")
        }
      },
      setLoginTip: function (id, msg) {
        var target = this.byId(id);
        if (!target) {
          return false;
        }
        if (msg) {
          target.value = "";
          target.setAttribute("placeholder", msg);
          target.className = "input-fail";
          target.className += " shake";
          target.focus();
          setTimeout(function () {
            target.className = target.className.replace("shake", "");
          }, 400)
        } else {
          target.setAttribute("placeholder", '');
          target.className = "";
        }
      },
      onShowForgotPsw: function (isShow) {
        this.byId("forgot_password").style.display = isShow ? "block" : "none";
      },
      onLogin: function (autoLogin) {
        var self = this;
        var passwordEl = this.byId('password'),
          btnSubmit = this.byId('login');
        var username = 'admin';
        ''
        if (passwordEl.value == "") {
          self.setLoginTip("password", "Password");
          return false;
        } else if (passwordEl.value.length > 64) {
          self.setLoginTip("password", "The password is incorrect.")
          return false;
        } else {
          self.setLoginTip("password")
        }
        passwordEl.className = '';
        btnSubmit.innerHTML = 'Logging...';
        btnSubmit.setAttribute('disabled', 'disabled');
        this.onPost({
          data: {
            method: "login",
            params: {
              password: !autoLogin ? GibberishAES.enc(passwordEl.value, k).replace(/\s+/g, '') : passwordEl
                .value,
              username: username,
              time: (new Date().getTime() / 1000).toFixed(0),
              encry: true,
              limit: '' === 'true'
            }
          },
          url: url,
          success: function (data) {
            if (!data || !data.sid) {
              btnSubmit.innerHTML = 'Log In';
              btnSubmit.removeAttribute('disabled');
              var errorTip = "The password is incorrect."
              if (typeof data === 'string') {
                self.setLoginTip('password')
                return self.setDiableLogin(data)
              }
              return self.setLoginTip("password", errorTip);
            }
            setTimeout(function () {
              window.location.href = '/cgi-bin/luci/;stok=' + data.token + INITPATH
            }, 300);
          },
          err: function (xhr) {
            // console.log(data)
            if (passwordEl.value.length > 64) {
              btnSubmit.innerHTML = 'Log In';
              btnSubmit.removeAttribute('disabled');
              self.setLoginTip("password", "The password is incorrect.")
            }
          }
        })
      },
      setDiableLogin: function (data) {
        var self = this
        var _tmp = data.split(/\s+/)
        var _seconds = +_tmp[4] || 60
        disableBtn('login', true)

        var sInt = setInterval(function () {
          self.byId('errorTimes').innerHTML = "Incorrected 3 times, re-enter after %d seconds".replace('%d', _seconds)
          if (_seconds-- <= 0) {
            self.byId('errorTimes').innerHTML = ""
            disableBtn('login', false)
            clearInterval(sInt)
          }
        }, 1000);

        function disableBtn(id, toggle) {
          var _dom = self.byId('login')
          _dom.disabled = toggle
          _dom.className = 'lg-font lg-btn-submit tc'
          _dom.className += toggle ? ' disable' : ''
        }
      },
      initEvent: function () {
        var self = this;
        this.byId("forgot_a").onclick = function () {
          self.onShowForgotPsw(true);
        }
        this.byId("forgot_close").onclick = function () {
          self.onShowForgotPsw(false);
        }
        this.byId("password").onkeyup = function (e) {
          if (e.keyCode == 13) self.onLogin();
        }
        this.byId("login").onclick = function () {
          self.onLogin();
        }
      },
      showPwd: function () {
        var self = this;
        this.byId("icon-pwd").onclick = function (e) {
          var _input = self.byId("password")
          var _dom = e.target
          if (_input.type === 'text') {
            _input.type = 'password'
            _dom.setAttribute("src", "/luci-static/eweb-ehr/static/image/hide.png")
          } else {
            _input.type = 'text'
            _dom.setAttribute("src", "/luci-static/eweb-ehr/static/image/open.png")
          }
        }
      }
    }
    Login.init();
  </script>
</body>

</html>