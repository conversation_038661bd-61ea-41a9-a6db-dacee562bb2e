#!/usr/bin/env node

/**
 * Test script to find and test the router restart functionality
 */

const axios = require('axios');
const CryptoJS = require('crypto-js');

// Configuration
const ROUTER_CONFIG = {
  ip: '*************',
  password: 'pcs2ass2ADM'
};

async function testRestartFunctionality() {
  console.log('=== Testing Router Restart Functionality ===\n');

  // Create axios instance
  const client = axios.create({
    baseURL: `http://${ROUTER_CONFIG.ip}`,
    timeout: 10000,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
    withCredentials: true,
  });

  let token = null;

  try {
    // Step 1: Login
    console.log('Step 1: Logging in...');

    const aesKey = 'RjYkhwzx$2018!';
    const encryptedPassword = CryptoJS.AES.encrypt(ROUTER_CONFIG.password, aesKey).toString();

    const loginData = {
      method: "login",
      params: {
        password: encryptedPassword,
        username: 'admin',
        time: Math.floor(Date.now() / 1000).toString(),
        encry: true,
        limit: false
      }
    };

    const loginResponse = await client.post('/cgi-bin/luci/api/auth', loginData);

    if (loginResponse.status === 200 && loginResponse.data && loginResponse.data.code === 0) {
      if (loginResponse.data.data && loginResponse.data.data.token) {
        token = loginResponse.data.data.token;
        console.log(`✅ Logged in successfully with token: ${token.substring(0, 8)}...\n`);
      }
    }

    if (!token) {
      throw new Error('Failed to extract authentication token');
    }

    // Step 2: Test different restart methods
    console.log('Step 2: Testing different restart methods...\n');

    // Method 1: Try API endpoint with system reboot
    console.log('Method 1: Testing API system reboot...');
    try {
      const systemRebootData = {
        method: 'reboot',
        params: {}
      };
      const systemResponse = await client.post(`/cgi-bin/luci/;stok=${token}/api/system`, systemRebootData);
      console.log(`✅ System API reboot response:`, systemResponse.status);
      console.log('Response data:', JSON.stringify(systemResponse.data, null, 2));
    } catch (error) {
      console.log(`❌ System API reboot failed:`, error.response?.status, error.message);
    }

    // Method 2: Try direct reboot API endpoint
    console.log('\nMethod 2: Testing direct reboot API...');
    try {
      const rebootData = {
        method: 'reboot',
        params: {}
      };
      const rebootResponse = await client.post(`/cgi-bin/luci/;stok=${token}/api/reboot`, rebootData);
      console.log(`✅ Direct reboot API response:`, rebootResponse.status);
      console.log('Response data:', JSON.stringify(rebootResponse.data, null, 2));
    } catch (error) {
      console.log(`❌ Direct reboot API failed:`, error.response?.status, error.message);
    }

    // Method 3: Try web interface reboot page
    console.log('\nMethod 3: Testing web interface reboot...');
    try {
      // First get the reboot page to see if there are any forms or tokens needed
      const rebootPageResponse = await client.get(`/cgi-bin/luci/;stok=${token}/admin/system/reboot`);
      console.log(`✅ Reboot page accessed:`, rebootPageResponse.status);

      // Check if the page contains actual reboot functionality or just login page
      if (rebootPageResponse.data.includes('reboot') && !rebootPageResponse.data.includes('loginPass')) {
        console.log('✅ Reboot page contains reboot functionality');

        // Look for form action or JavaScript that handles reboot
        const formMatch = rebootPageResponse.data.match(/<form[^>]*action="([^"]*)"[^>]*>/i);
        if (formMatch) {
          console.log('Found form action:', formMatch[1]);
        }

        // Look for reboot button or JavaScript
        const rebootButtonMatch = rebootPageResponse.data.match(/reboot|restart/gi);
        if (rebootButtonMatch) {
          console.log('Found reboot references:', rebootButtonMatch.length);
        }
      } else {
        console.log('❌ Reboot page shows login page - session may have expired');
      }
    } catch (error) {
      console.log(`❌ Web interface reboot failed:`, error.response?.status, error.message);
    }

    // Method 4: Try form-based reboot submission
    console.log('\nMethod 4: Testing form-based reboot...');
    try {
      // Change content type for form submission
      const formClient = axios.create({
        baseURL: `http://${ROUTER_CONFIG.ip}`,
        timeout: 10000,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        withCredentials: true,
      });

      const formData = new URLSearchParams();
      formData.append('token', token);
      formData.append('action', 'reboot');

      const formResponse = await formClient.post(`/cgi-bin/luci/;stok=${token}/admin/system/reboot`, formData);
      console.log(`✅ Form reboot response:`, formResponse.status);
      console.log('Response headers:', formResponse.headers);
    } catch (error) {
      console.log(`❌ Form reboot failed:`, error.response?.status, error.message);
    }

    // Method 5: Try simple GET request to reboot URL (some routers use this)
    console.log('\nMethod 5: Testing simple GET reboot...');
    try {
      const getRebootResponse = await client.get(`/cgi-bin/luci/;stok=${token}/admin/system/reboot?action=reboot`);
      console.log(`✅ GET reboot response:`, getRebootResponse.status);
    } catch (error) {
      console.log(`❌ GET reboot failed:`, error.response?.status, error.message);
    }

    // Method 6: Test if router actually reboots by checking connectivity
    console.log('\nMethod 6: Testing actual reboot by monitoring connectivity...');
    console.log('⚠️  WARNING: This will attempt to actually reboot the router!');
    console.log('Skipping actual reboot test to avoid disrupting network connectivity.');
    console.log('To test actual reboot, manually trigger one of the above methods and monitor router status.');

    console.log('\n🎉 Restart functionality testing completed!');
    console.log('\n📋 FINDINGS:');
    console.log('- API endpoints return HTML instead of JSON, suggesting no proper REST API');
    console.log('- Router appears to use web-based interface for management');
    console.log('- Need to implement web scraping approach for restart functionality');
    console.log('- Consider using form submission or direct URL access for reboot');

  } catch (error) {
    console.error('❌ Testing failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the test
testRestartFunctionality().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
