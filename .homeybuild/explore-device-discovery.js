#!/usr/bin/env node

/**
 * Explore router interface to find device discovery endpoints
 */

const axios = require('axios');
const CryptoJS = require('crypto-js');
const fs = require('fs');

// Configuration
const ROUTER_CONFIG = {
  ip: '*************',
  password: 'pcs2ass2ADM'
};

async function exploreDeviceDiscovery() {
  console.log('=== Exploring Device Discovery Endpoints ===\n');
  
  // Create axios instance
  const client = axios.create({
    baseURL: `http://${ROUTER_CONFIG.ip}`,
    timeout: 10000,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
    withCredentials: true,
  });

  let token = null;

  try {
    // Step 1: Login
    console.log('Step 1: Logging in...');
    
    const aesKey = 'RjYkhwzx$2018!';
    const encryptedPassword = CryptoJS.AES.encrypt(ROUTER_CONFIG.password, aesKey).toString();
    
    const loginData = {
      method: "login",
      params: {
        password: encryptedPassword,
        username: 'admin',
        time: Math.floor(Date.now() / 1000).toString(),
        encry: true,
        limit: false
      }
    };

    const loginResponse = await client.post('/cgi-bin/luci/api/auth', loginData);
    
    if (loginResponse.status === 200 && loginResponse.data && loginResponse.data.code === 0) {
      if (loginResponse.data.data && loginResponse.data.data.token) {
        token = loginResponse.data.data.token;
        console.log(`✅ Logged in successfully with token: ${token.substring(0, 8)}...\n`);
      }
    }
    
    if (!token) {
      throw new Error('Failed to extract authentication token');
    }

    // Step 2: Explore device-related pages
    console.log('Step 2: Exploring device-related pages...');
    const devicePages = [
      `/cgi-bin/luci/;stok=${token}/admin/status/overview`,
      `/cgi-bin/luci/;stok=${token}/admin/status/wireless`,
      `/cgi-bin/luci/;stok=${token}/admin/status/connections`,
      `/cgi-bin/luci/;stok=${token}/admin/network/dhcp`,
      `/cgi-bin/luci/;stok=${token}/admin/network/hosts`,
      `/cgi-bin/luci/;stok=${token}/admin/network/wireless`,
      `/cgi-bin/luci/;stok=${token}/admin/status/realtime`,
      `/cgi-bin/luci/;stok=${token}/admin/status/bandwidth`,
    ];

    for (const page of devicePages) {
      try {
        console.log(`\nTesting: ${page}`);
        const response = await client.get(page);
        
        if (response.status === 200 && response.data) {
          const content = response.data;
          
          // Check if this page contains device information
          const hasDeviceInfo = 
            content.includes('device') || 
            content.includes('client') || 
            content.includes('MAC') || 
            content.includes('IP') ||
            content.includes('hostname') ||
            content.includes('connected') ||
            content.includes('wireless') ||
            content.includes('ethernet');
          
          if (hasDeviceInfo) {
            console.log(`✅ ${page} - Contains device information`);
            
            // Save the page for analysis
            const filename = page.split('/').pop() + '.html';
            fs.writeFileSync(filename, content);
            console.log(`  💾 Saved to ${filename}`);
            
            // Look for specific patterns
            const patterns = [
              /MAC[:\s]*([0-9A-Fa-f:]{17})/gi,
              /IP[:\s]*(\d+\.\d+\.\d+\.\d+)/gi,
              /hostname[:\s]*([^\s<>"']+)/gi,
              /device[:\s]*name[:\s]*([^\s<>"']+)/gi,
              /client[:\s]*([^\s<>"']+)/gi
            ];
            
            patterns.forEach((pattern, index) => {
              const matches = content.match(pattern);
              if (matches && matches.length > 0) {
                console.log(`  📋 Pattern ${index + 1} matches:`, matches.slice(0, 3));
              }
            });
          } else {
            console.log(`❌ ${page} - No device information found`);
          }
        }
      } catch (error) {
        console.log(`❌ ${page} - Error: ${error.response?.status || error.message}`);
      }
      
      // Small delay
      await new Promise(resolve => setTimeout(resolve, 200));
    }

    // Step 3: Try API endpoints for device information
    console.log('\n\nStep 3: Testing API endpoints for device information...');
    const apiEndpoints = [
      `/cgi-bin/luci/;stok=${token}/api/network`,
      `/cgi-bin/luci/;stok=${token}/api/devices`,
      `/cgi-bin/luci/;stok=${token}/api/clients`,
      `/cgi-bin/luci/;stok=${token}/api/wireless`,
      `/cgi-bin/luci/;stok=${token}/api/dhcp`,
      `/cgi-bin/luci/;stok=${token}/api/status`,
    ];

    for (const endpoint of apiEndpoints) {
      try {
        console.log(`\nTesting API: ${endpoint}`);
        
        // Try GET request
        try {
          const getResponse = await client.get(endpoint);
          console.log(`✅ GET ${endpoint} - Status: ${getResponse.status}`);
          if (getResponse.data && typeof getResponse.data === 'object') {
            console.log(`  📋 Response:`, JSON.stringify(getResponse.data, null, 2).substring(0, 200) + '...');
          }
        } catch (getError) {
          console.log(`❌ GET ${endpoint} - ${getError.response?.status || 'Error'}`);
        }

        // Try POST with different methods
        const methods = ['getDevices', 'getClients', 'getConnected', 'list', 'status'];
        for (const method of methods) {
          try {
            const postData = { method: method, params: {} };
            const postResponse = await client.post(endpoint, postData);
            console.log(`✅ POST ${endpoint} (${method}) - Status: ${postResponse.status}`);
            if (postResponse.data && typeof postResponse.data === 'object') {
              console.log(`  📋 Response:`, JSON.stringify(postResponse.data, null, 2).substring(0, 200) + '...');
            }
          } catch (postError) {
            // Don't log every failed POST attempt to keep output clean
          }
        }
      } catch (error) {
        console.log(`❌ ${endpoint} - Error: ${error.message}`);
      }
      
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    console.log('\n🎉 Device discovery exploration completed!');

  } catch (error) {
    console.error('❌ Exploration failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the exploration
exploreDeviceDiscovery().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
