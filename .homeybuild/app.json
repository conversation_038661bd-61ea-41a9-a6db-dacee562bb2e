{"_comment": "This file is generated. Please edit .homeycompose/app.json instead.", "id": "com.ruijie.x32pro", "version": "1.0.0", "compatibility": ">=12.3.0", "sdk": 3, "platforms": ["local"], "name": {"en": "Ruijie X32-PRO"}, "description": {"en": "Control your Ruijie X32-PRO WiFi router with Homey"}, "category": ["internet"], "permissions": ["homey:manager:api"], "images": {"small": "/assets/images/small.png", "large": "/assets/images/large.png", "xlarge": "/assets/images/xlarge.png"}, "author": {"name": "Your Name", "email": "<EMAIL>"}, "contributing": {"donate": {"paypal": {"username": "YourPayPalUsername"}}}, "flow": {"triggers": [{"id": "connected_devices_changed", "title": {"en": "Connected devices changed"}, "hint": {"en": "Triggered when the list of connected devices changes."}, "args": [{"name": "device", "type": "device", "filter": "driver_id=router"}], "tokens": [{"name": "count", "type": "number", "title": {"en": "Count"}, "example": 5}, {"name": "devices", "type": "string", "title": {"en": "Devices"}}]}], "conditions": [{"id": "device_connected", "title": {"en": "A device !{{is|is not}} connected"}, "hint": {"en": "Checks if a device with the specified MAC address is connected to the router."}, "args": [{"name": "device", "type": "device", "filter": "driver_id=router"}, {"name": "mac_address", "type": "text", "placeholder": {"en": "AA:BB:CC:DD:EE:FF"}}]}], "actions": [{"id": "restart_router", "title": {"en": "Restart router"}, "hint": {"en": "Restarts the Ruijie X32-PRO router."}, "args": [{"name": "device", "type": "device", "filter": "driver_id=router"}]}, {"id": "toggle_guest_wifi", "title": {"en": "Turn guest WiFi !{{on|off}}"}, "hint": {"en": "Enables or disables the guest WiFi network."}, "args": [{"name": "device", "type": "device", "filter": "driver_id=router"}, {"name": "enabled", "type": "dropdown", "values": [{"id": "true", "label": {"en": "On"}}, {"id": "false", "label": {"en": "Off"}}]}]}]}, "drivers": [{"name": {"en": "Ruijie X32-PRO Router"}, "class": "networkrouter", "capabilities": ["onoff", "button.restart", "<PERSON><PERSON><PERSON>", "measure_uptime", "measure_connected_devices", "measure_cpu_usage", "measure_memory_usage"], "capabilitiesOptions": {"onoff": {"title": {"en": "Router Online"}}, "button.restart": {"title": {"en": "Restart Router"}}, "measure_uptime": {"title": {"en": "Uptime"}, "units": {"en": "seconds"}}}, "platforms": ["local"], "connectivity": [], "images": {"small": "/drivers/router/assets/images/small.png", "large": "/drivers/router/assets/images/large.png", "xlarge": "/drivers/router/assets/images/xlarge.png"}, "pair": [{"id": "list_devices", "template": "list_devices", "navigation": {"next": "add_devices"}}, {"id": "add_devices", "template": "add_devices"}], "settings": [{"type": "group", "label": {"en": "Router Connection"}, "children": [{"id": "ip", "type": "text", "label": {"en": "Router IP Address"}, "value": "*************"}, {"id": "username", "type": "text", "label": {"en": "Username"}, "value": "admin"}, {"id": "password", "type": "password", "label": {"en": "Password"}}]}, {"type": "group", "label": {"en": "Router Information"}, "children": [{"id": "firmware_version", "type": "label", "label": {"en": "Firmware Version"}, "value": "Unknown"}, {"id": "model", "type": "label", "label": {"en": "Model"}, "value": "X32-PRO"}, {"id": "wan_ip", "type": "label", "label": {"en": "WAN IP"}, "value": "0.0.0.0"}, {"id": "lan_ip", "type": "label", "label": {"en": "LAN IP"}, "value": "*************"}, {"id": "connected_devices_count", "type": "label", "label": {"en": "Connected Devices Count"}, "value": "0"}]}, {"type": "group", "label": {"en": "Connected Devices"}, "children": [{"id": "device_list", "type": "label", "label": {"en": "Device List"}, "value": "No devices connected"}, {"id": "wired_devices", "type": "label", "label": {"en": "Wired Devices"}, "value": "0"}, {"id": "wireless_devices", "type": "label", "label": {"en": "Wireless Devices"}, "value": "0"}]}], "id": "router"}], "capabilities": {"guestwifi": {"type": "boolean", "title": {"en": "Guest <PERSON><PERSON><PERSON><PERSON>"}, "getable": true, "setable": true, "uiComponent": "toggle", "icon": "/assets/capabilities/guestwifi.svg"}, "measure_connected_devices": {"type": "number", "title": {"en": "Connected Devices"}, "units": {"en": "devices"}, "getable": true, "setable": false, "uiComponent": "sensor", "icon": "/assets/capabilities/connected_devices.svg"}, "measure_cpu_usage": {"type": "number", "title": {"en": "CPU Usage"}, "units": {"en": "%"}, "getable": true, "setable": false, "uiComponent": "sensor", "min": 0, "max": 100, "icon": "/assets/capabilities/cpu_usage.svg"}, "measure_memory_usage": {"type": "number", "title": {"en": "Memory Usage"}, "units": {"en": "%"}, "getable": true, "setable": false, "uiComponent": "sensor", "min": 0, "max": 100, "icon": "/assets/capabilities/memory_usage.svg"}, "measure_uptime": {"type": "number", "title": {"en": "Uptime"}, "units": {"en": "seconds"}, "getable": true, "setable": false, "uiComponent": "sensor"}}}