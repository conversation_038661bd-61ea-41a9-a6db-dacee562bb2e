#!/usr/bin/env node

/**
 * Test the complete authentication flow including redirects and session handling
 */

const axios = require('axios');
const CryptoJS = require('crypto-js');

// Configuration
const ROUTER_CONFIG = {
  ip: '*************',
  password: 'admin'
};

async function testCompleteFlow() {
  console.log('=== Testing Complete Authentication Flow ===\n');
  
  // Create axios instance with cookie jar simulation
  const cookieJar = {};
  
  const client = axios.create({
    baseURL: `http://${ROUTER_CONFIG.ip}`,
    timeout: 10000,
    headers: {
      'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
      'Accept': 'application/json, text/javascript, */*; q=0.01',
      'Accept-Language': 'en-US,en;q=0.9',
    },
    withCredentials: true,
    maxRedirects: 0, // <PERSON><PERSON> redirects manually
  });

  // Add request interceptor to handle cookies
  client.interceptors.request.use(config => {
    const cookies = Object.entries(cookieJar).map(([key, value]) => `${key}=${value}`).join('; ');
    if (cookies) {
      config.headers.Cookie = cookies;
    }
    return config;
  });

  // Add response interceptor to capture cookies
  client.interceptors.response.use(
    response => {
      const setCookies = response.headers['set-cookie'];
      if (setCookies) {
        setCookies.forEach(cookie => {
          const [nameValue] = cookie.split(';');
          const [name, value] = nameValue.split('=');
          cookieJar[name.trim()] = value ? value.trim() : '';
        });
      }
      return response;
    },
    error => {
      // Handle redirects manually
      if (error.response && [301, 302, 303, 307, 308].includes(error.response.status)) {
        const location = error.response.headers.location;
        console.log(`Redirect to: ${location}`);
        
        // Capture cookies from redirect response
        const setCookies = error.response.headers['set-cookie'];
        if (setCookies) {
          setCookies.forEach(cookie => {
            const [nameValue] = cookie.split(';');
            const [name, value] = nameValue.split('=');
            cookieJar[name.trim()] = value ? value.trim() : '';
          });
        }
        
        return { 
          ...error.response, 
          isRedirect: true, 
          redirectLocation: location 
        };
      }
      return Promise.reject(error);
    }
  );

  try {
    // Step 1: Get initial login page
    console.log('Step 1: Getting initial login page...');
    const initialResponse = await client.get('/cgi-bin/luci/');
    console.log('Initial cookies:', cookieJar);

    // Step 2: Perform login with correct key
    console.log('\nStep 2: Performing login...');
    
    const correctKey = 'RjYkhwzx$2018!';
    const encryptedPassword = CryptoJS.AES.encrypt(ROUTER_CONFIG.password, correctKey).toString();
    
    const loginData = {
      method: "login",
      params: {
        password: encryptedPassword,
        username: 'admin',
        time: Math.floor(Date.now() / 1000).toString(),
        encry: true,
        limit: false
      }
    };

    console.log('Sending login request...');
    const loginResponse = await client.post('/cgi-bin/luci/api/auth', loginData, {
      headers: {
        'Content-Type': 'application/json',
        'Referer': `http://${ROUTER_CONFIG.ip}/cgi-bin/luci/`
      }
    });

    console.log('Login response:', JSON.stringify(loginResponse.data, null, 2));
    console.log('Cookies after login:', cookieJar);

    // Step 3: Check if we got a redirect or session info
    if (loginResponse.isRedirect) {
      console.log('\nStep 3: Following redirect...');
      const redirectResponse = await client.get(loginResponse.redirectLocation);
      console.log('Redirect response status:', redirectResponse.status);
      console.log('Cookies after redirect:', cookieJar);
    }

    // Step 4: Try to access main page again
    console.log('\nStep 4: Accessing main page after login...');
    const mainPageResponse = await client.get('/cgi-bin/luci/');
    
    if (mainPageResponse.data.includes('loginPass')) {
      console.log('❌ Still on login page');
      
      // Maybe we need to manually construct the session URL
      console.log('\nStep 5: Trying to find session token...');
      
      // Look for session token in cookies
      const sessionToken = cookieJar.stok || cookieJar.sysauth;
      if (sessionToken) {
        console.log(`Found session token in cookies: ${sessionToken}`);
        
        // Try accessing with session token
        const sessionUrl = `/cgi-bin/luci/;stok=${sessionToken}`;
        console.log(`Trying session URL: ${sessionUrl}`);
        
        const sessionResponse = await client.get(sessionUrl);
        if (!sessionResponse.data.includes('loginPass')) {
          console.log('🎉 Successfully accessed authenticated page!');
          
          // Save the page
          const fs = require('fs');
          fs.writeFileSync('authenticated-main-page.html', sessionResponse.data);
          console.log('Saved authenticated page to authenticated-main-page.html');
          
          // Test API access
          console.log('\nTesting API access...');
          try {
            const apiResponse = await client.post(`${sessionUrl}/api/system`, {
              method: 'get_system_info',
              params: {}
            });
            console.log('API Response:', JSON.stringify(apiResponse.data, null, 2));
          } catch (apiError) {
            console.log('API test failed:', apiError.message);
          }
        } else {
          console.log('❌ Session URL still shows login page');
        }
      } else {
        console.log('❌ No session token found in cookies');
        console.log('Available cookies:', Object.keys(cookieJar));
      }
    } else {
      console.log('🎉 Successfully authenticated! No longer on login page.');
      
      // Save the authenticated page
      const fs = require('fs');
      fs.writeFileSync('authenticated-main-page.html', mainPageResponse.data);
      console.log('Saved authenticated page');
      
      // Look for session token in the page
      const stokMatch = mainPageResponse.data.match(/stok=([a-zA-Z0-9]+)/);
      if (stokMatch) {
        console.log(`Found session token in page: ${stokMatch[1]}`);
      }
    }

    console.log('\n🎉 Complete flow test finished!');

  } catch (error) {
    console.error('❌ Complete flow test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response headers:', error.response.headers);
    }
  }
}

// Run the test
testCompleteFlow().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
