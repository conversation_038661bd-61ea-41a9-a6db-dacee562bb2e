#!/usr/bin/env node

/**
 * Test with wrong password to understand the response format
 */

const axios = require('axios');
const CryptoJS = require('crypto-js');

// Configuration
const ROUTER_CONFIG = {
  ip: '*************',
  correctPassword: 'admin',
  wrongPassword: 'wrongpassword'
};

async function testWrongPassword() {
  console.log('=== Testing Wrong Password Response ===\n');
  
  const client = axios.create({
    baseURL: `http://${ROUTER_CONFIG.ip}`,
    timeout: 10000,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
    withCredentials: true,
  });

  const correctKey = 'RjYkhwzx$2018!';

  try {
    // Test 1: Wrong password
    console.log('Test 1: Using wrong password...');
    const wrongEncryptedPassword = CryptoJS.AES.encrypt(ROUTER_CONFIG.wrongPassword, correctKey).toString();
    
    const wrongLoginData = {
      method: "login",
      params: {
        password: wrongEncryptedPassword,
        username: 'admin',
        time: Math.floor(Date.now() / 1000).toString(),
        encry: true,
        limit: false
      }
    };

    const wrongResponse = await client.post('/cgi-bin/luci/api/auth', wrongLoginData);
    console.log('Wrong password response:', JSON.stringify(wrongResponse.data, null, 2));
    console.log('Wrong password headers:', JSON.stringify(wrongResponse.headers, null, 2));

    // Test 2: Correct password
    console.log('\nTest 2: Using correct password...');
    const correctEncryptedPassword = CryptoJS.AES.encrypt(ROUTER_CONFIG.correctPassword, correctKey).toString();
    
    const correctLoginData = {
      method: "login",
      params: {
        password: correctEncryptedPassword,
        username: 'admin',
        time: Math.floor(Date.now() / 1000).toString(),
        encry: true,
        limit: false
      }
    };

    const correctResponse = await client.post('/cgi-bin/luci/api/auth', correctLoginData);
    console.log('Correct password response:', JSON.stringify(correctResponse.data, null, 2));
    console.log('Correct password headers:', JSON.stringify(correctResponse.headers, null, 2));

    // Test 3: Try different API endpoints after "successful" login
    console.log('\nTest 3: Testing API endpoints after login...');
    
    const testEndpoints = [
      '/cgi-bin/luci/api/system',
      '/cgi-bin/luci/api/network', 
      '/cgi-bin/luci/api/wireless',
      '/cgi-bin/luci/api/status'
    ];

    for (const endpoint of testEndpoints) {
      try {
        const response = await client.get(endpoint);
        console.log(`${endpoint}: ${response.status} - ${JSON.stringify(response.data)}`);
      } catch (error) {
        console.log(`${endpoint}: ${error.response?.status || 'Error'} - ${error.message}`);
      }
    }

    // Test 4: Try to understand if there's a different login endpoint
    console.log('\nTest 4: Testing alternative login methods...');
    
    // Maybe it's a form-based login after all?
    try {
      const formData = new URLSearchParams();
      formData.append('loginPass', ROUTER_CONFIG.correctPassword);
      
      const formResponse = await client.post('/cgi-bin/luci/', formData, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Referer': `http://${ROUTER_CONFIG.ip}/cgi-bin/luci/`
        }
      });
      
      console.log('Form login response status:', formResponse.status);
      console.log('Form login response headers:', JSON.stringify(formResponse.headers, null, 2));
      
      if (!formResponse.data.includes('loginPass')) {
        console.log('🎉 Form login succeeded!');
        
        // Save the page
        const fs = require('fs');
        fs.writeFileSync('form-login-success.html', formResponse.data);
        console.log('Saved form login success page');
        
        // Look for session token
        const stokMatch = formResponse.data.match(/stok=([a-zA-Z0-9]+)/);
        if (stokMatch) {
          console.log(`Found session token: ${stokMatch[1]}`);
          
          // Test API with this token
          try {
            const apiResponse = await client.post(`/cgi-bin/luci/;stok=${stokMatch[1]}/api/system`, {
              method: 'get_system_info',
              params: {}
            });
            console.log('API with form token:', JSON.stringify(apiResponse.data, null, 2));
          } catch (apiError) {
            console.log('API with form token failed:', apiError.message);
          }
        }
      } else {
        console.log('❌ Form login failed - still on login page');
      }
    } catch (formError) {
      console.log('Form login error:', formError.message);
    }

    console.log('\n🎉 Wrong password test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the test
testWrongPassword().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
