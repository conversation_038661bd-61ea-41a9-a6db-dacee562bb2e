#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to explore the Ruijie X32-PRO router's API endpoints
 * This helps us understand the correct API structure and authentication flow
 */

const axios = require('axios');
const CryptoJS = require('crypto-js');

// Configuration
const ROUTER_CONFIG = {
  ip: '*************',
  password: 'admin'
};

async function exploreApiEndpoints() {
  console.log('=== Ruijie X32-PRO API Explorer ===\n');
  
  // Create axios instance
  const client = axios.create({
    baseURL: `http://${ROUTER_CONFIG.ip}`,
    timeout: 10000,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
    withCredentials: true,
  });

  try {
    // Step 1: Try the login API
    console.log('Step 1: Testing login API...');
    
    const aesKey = 'eweb';
    const encryptedPassword = CryptoJS.AES.encrypt(ROUTER_CONFIG.password, aesKey).toString();
    
    const loginData = {
      method: "login",
      params: {
        password: encryptedPassword,
        username: 'admin',
        time: Math.floor(Date.now() / 1000).toString(),
        encry: true,
        limit: false
      }
    };

    console.log('Sending login request...');
    const loginResponse = await client.post('/cgi-bin/luci/api/auth', loginData);
    
    console.log('Login Response:');
    console.log('  Status:', loginResponse.status);
    console.log('  Headers:', JSON.stringify(loginResponse.headers, null, 2));
    console.log('  Data:', JSON.stringify(loginResponse.data, null, 2));
    
    // Check for cookies
    const cookies = loginResponse.headers['set-cookie'];
    if (cookies) {
      console.log('  Cookies:', cookies);
    }

    // Step 2: Try to access the main page after login
    console.log('\nStep 2: Testing main page access after login...');
    try {
      const mainPageResponse = await client.get('/cgi-bin/luci/');
      console.log('Main page after login:');
      console.log('  Status:', mainPageResponse.status);
      console.log('  Content length:', mainPageResponse.data.length);
      
      // Check if we're still on login page or if we got the main interface
      if (mainPageResponse.data.includes('loginPass')) {
        console.log('  Result: Still on login page - authentication may have failed');
      } else {
        console.log('  Result: Got different page - authentication may have succeeded');
        
        // Save the page for analysis
        const fs = require('fs');
        fs.writeFileSync('router-authenticated-page.html', mainPageResponse.data);
        console.log('  Saved authenticated page to router-authenticated-page.html');
      }
    } catch (error) {
      console.log('  Error accessing main page:', error.message);
    }

    // Step 3: Try different API endpoints
    console.log('\nStep 3: Testing various API endpoints...');
    
    const apiEndpoints = [
      '/cgi-bin/luci/api/system',
      '/cgi-bin/luci/api/network',
      '/cgi-bin/luci/api/wireless',
      '/cgi-bin/luci/api/status',
      '/cgi-bin/luci/api/device',
      '/cgi-bin/luci/api/admin',
      '/cgi-bin/luci/api/info'
    ];

    for (const endpoint of apiEndpoints) {
      try {
        console.log(`\nTesting ${endpoint}...`);
        
        // Try GET request
        try {
          const getResponse = await client.get(endpoint);
          console.log(`  GET ${endpoint}: Status ${getResponse.status}, Length: ${getResponse.data.length}`);
          if (getResponse.data && typeof getResponse.data === 'object') {
            console.log(`  GET Response:`, JSON.stringify(getResponse.data, null, 2));
          }
        } catch (getError) {
          console.log(`  GET ${endpoint}: ${getError.response?.status || 'Error'} - ${getError.message}`);
        }

        // Try POST request with basic system info request
        try {
          const postData = {
            method: "get_system_info",
            params: {}
          };
          const postResponse = await client.post(endpoint, postData);
          console.log(`  POST ${endpoint}: Status ${postResponse.status}`);
          if (postResponse.data && typeof postResponse.data === 'object') {
            console.log(`  POST Response:`, JSON.stringify(postResponse.data, null, 2));
          }
        } catch (postError) {
          console.log(`  POST ${endpoint}: ${postError.response?.status || 'Error'} - ${postError.message}`);
        }
        
        // Small delay to avoid overwhelming the router
        await new Promise(resolve => setTimeout(resolve, 200));
      } catch (error) {
        console.log(`  Error testing ${endpoint}:`, error.message);
      }
    }

    // Step 4: Try to find working endpoints by examining the login page JavaScript
    console.log('\nStep 4: Looking for API endpoints in router JavaScript...');
    try {
      const loginPageResponse = await client.get('/cgi-bin/luci/');
      const loginPageContent = loginPageResponse.data;
      
      // Look for API URLs in the JavaScript
      const apiUrlMatches = loginPageContent.match(/\/cgi-bin\/luci\/[^"'\s]+/g);
      if (apiUrlMatches) {
        const uniqueUrls = [...new Set(apiUrlMatches)];
        console.log('Found potential API URLs in login page:');
        uniqueUrls.forEach(url => console.log(`  ${url}`));
      }
      
      // Look for method names
      const methodMatches = loginPageContent.match(/"method":\s*"([^"]+)"/g);
      if (methodMatches) {
        const uniqueMethods = [...new Set(methodMatches)];
        console.log('Found potential API methods:');
        uniqueMethods.forEach(method => console.log(`  ${method}`));
      }
    } catch (error) {
      console.log('Error analyzing login page:', error.message);
    }

    console.log('\n🎉 API exploration completed!');

  } catch (error) {
    console.error('❌ API exploration failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the exploration
exploreApiEndpoints().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
