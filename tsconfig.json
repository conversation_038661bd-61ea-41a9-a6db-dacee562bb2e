{"compilerOptions": {"target": "es2018", "module": "commonjs", "lib": ["es2018"], "outDir": "build", "rootDir": ".", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "sourceMap": true}, "include": ["lib/**/*", "drivers/**/*", "app.ts"], "exclude": ["node_modules", "build", "test-*.js", "explore-*.js", "save-*.js", "*.html"]}