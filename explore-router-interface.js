#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to explore the Ruijie X32-PRO router's LuCI interface
 * This helps us find the correct URLs for status, wireless, and other pages
 */

const axios = require('axios');
const CryptoJS = require('crypto-js');

// Configuration
const ROUTER_CONFIG = {
  ip: '*************',
  password: 'pcs2ass2ADM'  // Updated to use the correct password
};

async function exploreRouterInterface() {
  console.log('=== Ruijie X32-PRO Router Interface Explorer ===\n');

  // Create axios instance
  const client = axios.create({
    baseURL: `http://${ROUTER_CONFIG.ip}`,
    timeout: 10000,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
    withCredentials: true,
  });

  let token = null;

  try {
    // Step 1: Login using the correct authentication method
    console.log('Step 1: Logging in...');

    // Use the AES key from the router's JavaScript
    const aesKey = 'RjYkhwzx$2018!';
    const encryptedPassword = CryptoJS.AES.encrypt(ROUTER_CONFIG.password, aesKey).toString();

    const loginData = {
      method: "login",
      params: {
        password: encryptedPassword,
        username: 'admin',
        time: Math.floor(Date.now() / 1000).toString(),
        encry: true,
        limit: false
      }
    };

    const loginResponse = await client.post('/cgi-bin/luci/api/auth', loginData);

    console.log('Login response:', JSON.stringify(loginResponse.data, null, 2));

    if (loginResponse.status === 200 && loginResponse.data && loginResponse.data.code === 0) {
      // Extract token from response data
      if (loginResponse.data.data && loginResponse.data.data.token) {
        token = loginResponse.data.data.token;
        console.log(`✅ Logged in successfully with token: ${token.substring(0, 8)}...\n`);
      } else {
        // Try to extract from cookies as fallback
        const setCookieHeader = loginResponse.headers['set-cookie'];
        if (setCookieHeader) {
          const sessionCookie = setCookieHeader.find((cookie) => cookie.includes('stok='));
          if (sessionCookie) {
            const tokenMatch = sessionCookie.match(/stok=([^;]+)/);
            if (tokenMatch && tokenMatch[1]) {
              token = tokenMatch[1];
              console.log(`✅ Logged in successfully with token from cookie: ${token.substring(0, 8)}...\n`);
            }
          }
        }
      }
    }

    if (!token) {
      throw new Error('Failed to extract authentication token');
    }

    // Step 2: Get the main page after login to see available menu items
    console.log('Step 2: Exploring main interface...');
    const mainPageResponse = await client.get(`/cgi-bin/luci/;stok=${token}`);

    // Save the main page for analysis
    const fs = require('fs');
    fs.writeFileSync('router-main-page.html', mainPageResponse.data);
    console.log('✅ Main page saved to router-main-page.html\n');

    // Step 3: Try common LuCI paths with authentication token
    console.log('Step 3: Testing common LuCI paths...');
    const commonPaths = [
      `/cgi-bin/luci/;stok=${token}/admin`,
      `/cgi-bin/luci/;stok=${token}/admin/status`,
      `/cgi-bin/luci/;stok=${token}/admin/system`,
      `/cgi-bin/luci/;stok=${token}/admin/network`,
      `/cgi-bin/luci/;stok=${token}/admin/network/wireless`,
      `/cgi-bin/luci/;stok=${token}/admin/services`,
      `/cgi-bin/luci/;stok=${token}/admin/status/overview`,
      `/cgi-bin/luci/;stok=${token}/admin/status/routes`,
      `/cgi-bin/luci/;stok=${token}/admin/status/iptables`,
      `/cgi-bin/luci/;stok=${token}/admin/status/processes`,
      `/cgi-bin/luci/;stok=${token}/admin/status/realtime`,
      `/cgi-bin/luci/;stok=${token}/admin/status/load`,
      `/cgi-bin/luci/;stok=${token}/admin/status/bandwidth`,
      `/cgi-bin/luci/;stok=${token}/admin/status/wireless`,
      `/cgi-bin/luci/;stok=${token}/admin/status/connections`,
      `/cgi-bin/luci/;stok=${token}/admin/network/dhcp`,
      `/cgi-bin/luci/;stok=${token}/admin/network/hosts`,
      `/cgi-bin/luci/;stok=${token}/admin/network/routes`,
      `/cgi-bin/luci/;stok=${token}/admin/network/firewall`,
      `/cgi-bin/luci/;stok=${token}/admin/system/admin`,
      `/cgi-bin/luci/;stok=${token}/admin/system/system`,
      `/cgi-bin/luci/;stok=${token}/admin/system/packages`,
      `/cgi-bin/luci/;stok=${token}/admin/system/startup`,
      `/cgi-bin/luci/;stok=${token}/admin/system/crontab`,
      `/cgi-bin/luci/;stok=${token}/admin/system/mounts`,
      `/cgi-bin/luci/;stok=${token}/admin/system/leds`,
      `/cgi-bin/luci/;stok=${token}/admin/system/flashops`,
      `/cgi-bin/luci/;stok=${token}/admin/system/reboot`
    ];

    // Also test API endpoints for restart functionality
    const apiEndpoints = [
      `/cgi-bin/luci/;stok=${token}/api/system`,
      `/cgi-bin/luci/;stok=${token}/api/admin`,
      `/cgi-bin/luci/;stok=${token}/api/reboot`
    ];

    const workingPaths = [];
    const failedPaths = [];

    // Test web interface paths
    for (const path of commonPaths) {
      try {
        const response = await client.get(path);
        if (response.status === 200) {
          workingPaths.push({
            path: path,
            title: extractTitle(response.data),
            hasForm: response.data.includes('<form'),
            hasTable: response.data.includes('<table'),
            hasRebootButton: response.data.includes('reboot') || response.data.includes('restart'),
            size: response.data.length,
            type: 'web'
          });
          console.log(`✅ ${path} - ${extractTitle(response.data)}`);

          // If this is the reboot page, save it for analysis
          if (path.includes('reboot')) {
            const fs = require('fs');
            fs.writeFileSync('router-reboot-page.html', response.data);
            console.log(`  💾 Saved reboot page content for analysis`);
          }
        }
      } catch (error) {
        if (error.response && error.response.status === 404) {
          failedPaths.push(path);
          console.log(`❌ ${path} - 404 Not Found`);
        } else {
          console.log(`⚠️  ${path} - ${error.message}`);
        }
      }

      // Small delay to avoid overwhelming the router
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // Test API endpoints
    console.log('\nStep 4: Testing API endpoints for restart functionality...');
    for (const endpoint of apiEndpoints) {
      try {
        // Test GET request
        const getResponse = await client.get(endpoint);
        if (getResponse.status === 200) {
          workingPaths.push({
            path: endpoint,
            title: 'API Endpoint',
            hasForm: false,
            hasTable: false,
            hasRebootButton: false,
            size: JSON.stringify(getResponse.data).length,
            type: 'api-get',
            data: getResponse.data
          });
          console.log(`✅ GET ${endpoint} - Success`);
        }
      } catch (error) {
        console.log(`❌ GET ${endpoint} - ${error.response?.status || 'Error'}`);
      }

      // Test POST request with reboot method
      try {
        const postData = {
          method: 'reboot',
          params: {}
        };
        const postResponse = await client.post(endpoint, postData);
        console.log(`✅ POST ${endpoint} (reboot method) - Status: ${postResponse.status}`);
        console.log(`  Response:`, JSON.stringify(postResponse.data, null, 2));
      } catch (error) {
        console.log(`❌ POST ${endpoint} (reboot method) - ${error.response?.status || 'Error'}`);
        if (error.response?.data) {
          console.log(`  Error response:`, JSON.stringify(error.response.data, null, 2));
        }
      }

      // Small delay
      await new Promise(resolve => setTimeout(resolve, 200));
    }

    console.log('\n=== SUMMARY ===');
    console.log(`Working paths: ${workingPaths.length}`);
    console.log(`Failed paths: ${failedPaths.length}\n`);

    if (workingPaths.length > 0) {
      console.log('📋 Working paths with details:');
      workingPaths.forEach(item => {
        console.log(`  ${item.path}`);
        console.log(`    Title: ${item.title}`);
        console.log(`    Has forms: ${item.hasForm}`);
        console.log(`    Has tables: ${item.hasTable}`);
        console.log(`    Size: ${item.size} bytes`);
        console.log('');
      });

      // Save detailed info about promising paths
      const promisingPaths = workingPaths.filter(item =>
        item.path.includes('status') ||
        item.path.includes('wireless') ||
        item.path.includes('network') ||
        item.path.includes('system')
      );

      console.log('💾 Saving content of promising paths...');
      for (const item of promisingPaths) {
        try {
          const response = await client.get(item.path);
          const filename = `router-page-${item.path.replace(/[^a-zA-Z0-9]/g, '_')}.html`;
          fs.writeFileSync(filename, response.data);
          console.log(`  Saved ${item.path} to ${filename}`);
        } catch (error) {
          console.log(`  Failed to save ${item.path}: ${error.message}`);
        }
      }
    }

    console.log('\n🎉 Exploration completed!');
    console.log('Check the saved HTML files to understand the router interface structure.');

  } catch (error) {
    console.error('❌ Exploration failed:', error.message);
  }
}

function extractTitle(html) {
  const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
  if (titleMatch && titleMatch[1]) {
    return titleMatch[1].trim();
  }

  // Try to find h1, h2, or other heading tags
  const headingMatch = html.match(/<h[1-6][^>]*>([^<]+)<\/h[1-6]>/i);
  if (headingMatch && headingMatch[1]) {
    return headingMatch[1].trim();
  }

  return 'Unknown';
}

// Run the exploration
exploreRouterInterface().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
