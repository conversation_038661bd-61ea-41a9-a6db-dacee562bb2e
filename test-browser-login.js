#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to test login by exactly mimicking what the browser does
 */

const axios = require('axios');
const CryptoJS = require('crypto-js');

// Configuration
const ROUTER_CONFIG = {
  ip: '*************',
  password: 'admin'
};

// GibberishAES implementation (simplified)
function gibberishAESEncrypt(string, pass) {
  // This is a simplified version of what GibberishAES does
  // The actual implementation is more complex, but this should work for basic cases
  try {
    const encrypted = CryptoJS.AES.encrypt(string, pass, {
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    });
    return encrypted.toString();
  } catch (error) {
    console.error('Encryption error:', error);
    return string;
  }
}

async function testBrowserLogin() {
  console.log('=== Testing Browser-like Login ===\n');
  
  // Create axios instance that behaves more like a browser
  const client = axios.create({
    baseURL: `http://${ROUTER_CONFIG.ip}`,
    timeout: 10000,
    headers: {
      'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      'Accept': 'application/json, text/javascript, */*; q=0.01',
      'Accept-Language': 'en-US,en;q=0.9',
      'Accept-Encoding': 'gzip, deflate',
      'Connection': 'keep-alive',
    },
    withCredentials: true,
  });

  try {
    // Step 1: Get the login page first (like a browser would)
    console.log('Step 1: Getting login page...');
    const loginPageResponse = await client.get('/cgi-bin/luci/');
    console.log('Login page loaded successfully');

    // Step 2: Try different encryption methods
    console.log('\nStep 2: Testing different password encryption methods...');
    
    const aesKey = 'eweb';
    const encryptionMethods = [
      {
        name: 'CryptoJS AES (default)',
        encrypt: (password) => CryptoJS.AES.encrypt(password, aesKey).toString()
      },
      {
        name: 'CryptoJS AES with CBC mode',
        encrypt: (password) => gibberishAESEncrypt(password, aesKey)
      },
      {
        name: 'Plain password (no encryption)',
        encrypt: (password) => password
      },
      {
        name: 'Base64 encoded password',
        encrypt: (password) => Buffer.from(password).toString('base64')
      }
    ];

    for (const method of encryptionMethods) {
      console.log(`\nTrying ${method.name}...`);
      
      const encryptedPassword = method.encrypt(ROUTER_CONFIG.password);
      console.log(`Encrypted password: ${encryptedPassword.substring(0, 20)}...`);
      
      const loginData = {
        method: "login",
        params: {
          password: encryptedPassword,
          username: 'admin',
          time: Math.floor(Date.now() / 1000).toString(),
          encry: method.name !== 'Plain password (no encryption)',
          limit: false
        }
      };

      try {
        const loginResponse = await client.post('/cgi-bin/luci/api/auth', loginData, {
          headers: {
            'Content-Type': 'application/json',
            'Referer': `http://${ROUTER_CONFIG.ip}/cgi-bin/luci/`
          }
        });

        console.log(`Response: ${JSON.stringify(loginResponse.data)}`);
        
        // Check if we got a different response
        if (loginResponse.data.code === 0) {
          console.log('✅ Login API returned success');
          
          // Try to access the main page
          const mainPageResponse = await client.get('/cgi-bin/luci/');
          if (!mainPageResponse.data.includes('loginPass')) {
            console.log('🎉 Successfully authenticated! No longer on login page.');
            
            // Save the authenticated page
            const fs = require('fs');
            fs.writeFileSync(`authenticated-page-${method.name.replace(/[^a-zA-Z0-9]/g, '_')}.html`, mainPageResponse.data);
            console.log(`Saved authenticated page for ${method.name}`);
            
            // Try to find the session token in the page
            const stokMatch = mainPageResponse.data.match(/stok=([a-zA-Z0-9]+)/);
            if (stokMatch) {
              console.log(`Found session token: ${stokMatch[1]}`);
              
              // Test API access with the token
              console.log('Testing API access with token...');
              try {
                const apiResponse = await client.post(`/cgi-bin/luci/;stok=${stokMatch[1]}/api/system`, {
                  method: 'get_system_info',
                  params: {}
                });
                console.log('API Response:', JSON.stringify(apiResponse.data, null, 2));
              } catch (apiError) {
                console.log('API test failed:', apiError.message);
              }
            }
            
            break; // Stop trying other methods
          } else {
            console.log('❌ Still on login page after authentication');
          }
        } else {
          console.log(`❌ Login failed with code: ${loginResponse.data.code}`);
        }
      } catch (error) {
        console.log(`❌ Login request failed: ${error.message}`);
      }
      
      // Small delay between attempts
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // Step 3: Try to understand the exact JavaScript flow
    console.log('\nStep 3: Analyzing the login JavaScript...');
    
    const loginPageContent = loginPageResponse.data;
    
    // Extract the exact encryption key
    const keyMatch = loginPageContent.match(/GibberishAES\.dec\('([^']+)',\s*'([^']+)'\)/);
    if (keyMatch) {
      console.log(`Found encrypted key: ${keyMatch[1]}`);
      console.log(`Decryption key: ${keyMatch[2]}`);
      
      // Try to decrypt the key
      try {
        const decryptedKey = CryptoJS.AES.decrypt(keyMatch[1], keyMatch[2]).toString(CryptoJS.enc.Utf8);
        console.log(`Decrypted key: ${decryptedKey}`);
        
        if (decryptedKey && decryptedKey !== aesKey) {
          console.log('Found different AES key, testing with it...');
          
          const newEncryptedPassword = CryptoJS.AES.encrypt(ROUTER_CONFIG.password, decryptedKey).toString();
          const loginData = {
            method: "login",
            params: {
              password: newEncryptedPassword,
              username: 'admin',
              time: Math.floor(Date.now() / 1000).toString(),
              encry: true,
              limit: false
            }
          };

          const loginResponse = await client.post('/cgi-bin/luci/api/auth', loginData);
          console.log(`Response with correct key: ${JSON.stringify(loginResponse.data)}`);
        }
      } catch (decryptError) {
        console.log('Failed to decrypt key:', decryptError.message);
      }
    }

    console.log('\n🎉 Browser login test completed!');

  } catch (error) {
    console.error('❌ Browser login test failed:', error.message);
  }
}

// Run the test
testBrowserLogin().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
