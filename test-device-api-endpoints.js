#!/usr/bin/env node

/**
 * Test specific API endpoints for device discovery
 */

const axios = require('axios');
const CryptoJS = require('crypto-js');

// Configuration
const ROUTER_CONFIG = {
  ip: '*************',
  password: 'pcs2ass2ADM'
};

async function testDeviceApiEndpoints() {
  console.log('=== Testing Device API Endpoints ===\n');
  
  // Create axios instance
  const client = axios.create({
    baseURL: `http://${ROUTER_CONFIG.ip}`,
    timeout: 10000,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
    withCredentials: true,
  });

  let token = null;

  try {
    // Step 1: Login
    console.log('Step 1: Logging in...');
    
    const aesKey = 'RjYkhwzx$2018!';
    const encryptedPassword = CryptoJS.AES.encrypt(ROUTER_CONFIG.password, aesKey).toString();
    
    const loginData = {
      method: "login",
      params: {
        password: encryptedPassword,
        username: 'admin',
        time: Math.floor(Date.now() / 1000).toString(),
        encry: true,
        limit: false
      }
    };

    const loginResponse = await client.post('/cgi-bin/luci/api/auth', loginData);
    
    if (loginResponse.status === 200 && loginResponse.data && loginResponse.data.code === 0) {
      if (loginResponse.data.data && loginResponse.data.data.token) {
        token = loginResponse.data.data.token;
        console.log(`✅ Logged in successfully with token: ${token.substring(0, 8)}...\n`);
      }
    }
    
    if (!token) {
      throw new Error('Failed to extract authentication token');
    }

    // Step 2: Test specific API methods that might return device information
    console.log('Step 2: Testing specific API methods for device information...\n');

    const testCases = [
      {
        endpoint: `/cgi-bin/luci/;stok=${token}/api/network`,
        methods: ['getDevices', 'getClients', 'getConnected', 'list', 'status', 'dhcp']
      },
      {
        endpoint: `/cgi-bin/luci/;stok=${token}/api/devices`,
        methods: ['list', 'getAll', 'getConnected', 'status']
      },
      {
        endpoint: `/cgi-bin/luci/;stok=${token}/api/clients`,
        methods: ['list', 'getAll', 'getConnected', 'wireless', 'wired']
      },
      {
        endpoint: `/cgi-bin/luci/;stok=${token}/api/wireless`,
        methods: ['getClients', 'getConnected', 'status', 'list']
      },
      {
        endpoint: `/cgi-bin/luci/;stok=${token}/api/dhcp`,
        methods: ['getLeases', 'getClients', 'list', 'status']
      },
      {
        endpoint: `/cgi-bin/luci/;stok=${token}/api/status`,
        methods: ['overview', 'network', 'wireless', 'devices']
      }
    ];

    for (const testCase of testCases) {
      console.log(`\n🔍 Testing endpoint: ${testCase.endpoint}`);
      
      for (const method of testCase.methods) {
        try {
          const postData = {
            method: method,
            params: {}
          };
          
          const response = await client.post(testCase.endpoint, postData);
          
          if (response.status === 200 && response.data) {
            // Check if response is JSON and contains useful data
            if (typeof response.data === 'object' && response.data !== null) {
              console.log(`✅ ${method}: JSON response received`);
              console.log(`   Code: ${response.data.code || 'N/A'}`);
              console.log(`   Data type: ${typeof response.data.data}`);
              
              if (response.data.data) {
                if (Array.isArray(response.data.data)) {
                  console.log(`   Array length: ${response.data.data.length}`);
                  if (response.data.data.length > 0) {
                    console.log(`   Sample item:`, JSON.stringify(response.data.data[0], null, 2).substring(0, 200) + '...');
                  }
                } else if (typeof response.data.data === 'object') {
                  console.log(`   Object keys:`, Object.keys(response.data.data));
                  console.log(`   Sample data:`, JSON.stringify(response.data.data, null, 2).substring(0, 200) + '...');
                } else {
                  console.log(`   Data:`, response.data.data);
                }
              }
              
              if (response.data.error) {
                console.log(`   Error:`, response.data.error);
              }
            } else {
              console.log(`❌ ${method}: Non-JSON response (likely HTML)`);
            }
          } else {
            console.log(`❌ ${method}: HTTP ${response.status}`);
          }
        } catch (error) {
          console.log(`❌ ${method}: ${error.response?.status || 'Error'} - ${error.message}`);
        }
        
        // Small delay between requests
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    // Step 3: Test some common router API patterns
    console.log('\n\nStep 3: Testing common router API patterns...\n');
    
    const commonPatterns = [
      { method: 'get', params: { module: 'network', action: 'devices' } },
      { method: 'get', params: { module: 'wireless', action: 'clients' } },
      { method: 'get', params: { module: 'dhcp', action: 'leases' } },
      { method: 'call', params: { service: 'network', method: 'devices' } },
      { method: 'call', params: { service: 'wireless', method: 'clients' } },
      { method: 'exec', params: { command: 'cat /proc/net/arp' } },
      { method: 'exec', params: { command: 'iwinfo' } }
    ];

    for (const pattern of commonPatterns) {
      try {
        console.log(`\n🔍 Testing pattern: ${JSON.stringify(pattern)}`);
        const response = await client.post(`/cgi-bin/luci/;stok=${token}/api/network`, pattern);
        
        if (response.status === 200 && response.data && typeof response.data === 'object') {
          console.log(`✅ Pattern successful`);
          console.log(`   Response:`, JSON.stringify(response.data, null, 2).substring(0, 300) + '...');
        } else {
          console.log(`❌ Pattern failed: Non-JSON response`);
        }
      } catch (error) {
        console.log(`❌ Pattern failed: ${error.response?.status || 'Error'}`);
      }
      
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    console.log('\n🎉 Device API endpoint testing completed!');

  } catch (error) {
    console.error('❌ Testing failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the test
testDeviceApiEndpoints().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
