#!/usr/bin/env node

/**
 * Save router pages properly using the RouterApiClient's getPageContent method
 */

const { RouterApiClient } = require('./build/lib/RouterApiClient');
const fs = require('fs');

// Configuration
const ROUTER_CONFIG = {
  ip: '*************',
  username: 'admin',
  password: 'pcs2ass2ADM'
};

async function saveRouterPagesProperly() {
  console.log('=== Saving Router Pages Properly ===\n');
  
  let client = null;

  try {
    // Step 1: Create and initialize client
    console.log('Step 1: Creating RouterApiClient...');
    client = new RouterApiClient(ROUTER_CONFIG.ip, ROUTER_CONFIG.username, ROUTER_CONFIG.password);
    
    // Step 2: Initialize connection
    console.log('Step 2: Initializing connection...');
    await client.init();
    console.log('✅ Connection initialized successfully\n');

    // Step 3: Save device-related pages using the proper method
    console.log('Step 3: Saving device-related pages using getPageContent method...\n');
    
    const pagesToSave = [
      {
        name: 'dhcp',
        path: '/admin/network/dhcp',
        description: 'DHCP leases page'
      },
      {
        name: 'wireless',
        path: '/admin/status/wireless',
        description: 'Wireless status page'
      },
      {
        name: 'overview',
        path: '/admin/status/overview',
        description: 'Status overview page'
      },
      {
        name: 'connections',
        path: '/admin/status/connections',
        description: 'Network connections page'
      },
      {
        name: 'hosts',
        path: '/admin/network/hosts',
        description: 'Network hosts page'
      },
      {
        name: 'main',
        path: '',
        description: 'Main authenticated page'
      }
    ];

    for (const page of pagesToSave) {
      try {
        console.log(`📄 Saving ${page.description}...`);
        
        // Use the RouterApiClient's getPageContent method
        const content = await client.getPageContent(page.path);
        
        const filename = `router-${page.name}-page-proper.html`;
        fs.writeFileSync(filename, content);
        console.log(`✅ Saved ${page.description} to ${filename}`);
        
        // Analyze the content briefly
        const hasDeviceKeywords = [
          'device', 'client', 'MAC', 'IP', 'hostname', 
          'connected', 'wireless', 'ethernet', 'lease', 'dhcp'
        ].some(keyword => content.toLowerCase().includes(keyword.toLowerCase()));
        
        if (hasDeviceKeywords) {
          console.log(`  📋 Contains device-related keywords`);
          
          // Look for table structures
          const tableCount = (content.match(/<table/gi) || []).length;
          const rowCount = (content.match(/<tr/gi) || []).length;
          console.log(`  📊 Tables: ${tableCount}, Rows: ${rowCount}`);
          
          // Look for MAC addresses
          const macMatches = content.match(/[0-9A-Fa-f:]{17}/g) || [];
          console.log(`  🔍 MAC addresses found: ${macMatches.length}`);
          if (macMatches.length > 0) {
            console.log(`    Sample MACs: ${macMatches.slice(0, 3).join(', ')}`);
          }
          
          // Look for IP addresses
          const ipMatches = content.match(/\d+\.\d+\.\d+\.\d+/g) || [];
          console.log(`  🌐 IP addresses found: ${ipMatches.length}`);
          if (ipMatches.length > 0) {
            console.log(`    Sample IPs: ${ipMatches.slice(0, 3).join(', ')}`);
          }

          // Look for JSON data
          const jsonMatches = content.match(/\{[^{}]*"[^"]*"[^{}]*\}/g) || [];
          console.log(`  📊 JSON objects found: ${jsonMatches.length}`);
          
          // Check if it's a login page
          const isLoginPage = content.includes('loginPass') || content.includes('Log In') || content.includes('password');
          if (isLoginPage) {
            console.log(`  ⚠️  This appears to be a login page - authentication may have failed`);
          }
        } else {
          console.log(`  ⚠️  No device-related keywords found`);
        }
        
        // Check content length
        console.log(`  📏 Content length: ${content.length} characters`);
        
      } catch (error) {
        console.log(`❌ Error saving ${page.description}:`, error.message);
      }
      
      console.log(''); // Empty line for readability
    }

    console.log('🎉 Page saving completed!');
    console.log('\n📋 ANALYSIS INSTRUCTIONS:');
    console.log('1. Open the saved HTML files in a text editor or browser');
    console.log('2. Look for device information in tables, lists, or JSON data');
    console.log('3. If pages show login forms, the authentication method needs adjustment');
    console.log('4. Look for JavaScript that loads device data dynamically');
    console.log('5. Update the parsing logic in RouterApiClient accordingly');
    console.log('\n📁 Saved files:');
    console.log('- router-dhcp-page-proper.html (DHCP leases)');
    console.log('- router-wireless-page-proper.html (Wireless clients)');
    console.log('- router-overview-page-proper.html (Status overview)');
    console.log('- router-connections-page-proper.html (Network connections)');
    console.log('- router-hosts-page-proper.html (Network hosts)');
    console.log('- router-main-page-proper.html (Main authenticated page)');

  } catch (error) {
    console.error('❌ Analysis failed:', error.message);
    if (error.stack) {
      console.error('Stack trace:', error.stack);
    }
  } finally {
    // Clean up
    if (client) {
      try {
        client.stopConnectionCheck();
        console.log('\n🧹 Cleaned up client resources');
      } catch (cleanupError) {
        console.log('⚠️  Error during cleanup:', cleanupError.message);
      }
    }
  }
}

// Run the analysis
saveRouterPagesProperly().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
