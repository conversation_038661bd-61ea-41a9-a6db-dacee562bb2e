<script>
  Homey.setTitle('Enter Router Credentials');

  // Initialize form data
  const formData = {
    ip: '*************',
    password: 'pcs2ass2ADM', // Pre-fill with the correct password for this router
  };

  // Update form data when input changes
  function updateFormData(key, value) {
    formData[key] = value;
    document.getElementById('submit-button').disabled = !formData.ip || !formData.password;
  }

  // Handle form submission
  async function onSubmit() {
    try {
      document.getElementById('submit-button').disabled = true;
      document.getElementById('status').textContent = 'Connecting to router...';

      // Validate router credentials
      const result = await Homey.emit('validate_router_credentials', formData);

      if (result) {
        document.getElementById('status').textContent = 'Connection successful!';
        Homey.nextView();
      } else {
        document.getElementById('status').textContent = 'Failed to connect to router. Please check your credentials.';
        document.getElementById('submit-button').disabled = false;
      }
    } catch (error) {
      document.getElementById('status').textContent = `Error: ${error.message || 'Unknown error'}`;
      document.getElementById('submit-button').disabled = false;
    }
  }

  // Initialize the form when the page loads
  function initForm() {
    // Set initial button state
    document.getElementById('submit-button').disabled = !formData.ip || !formData.password;
  }

  // Call initForm when the page loads
  document.addEventListener('DOMContentLoaded', initForm);
</script>

<style>
  .form-group {
    margin-bottom: 1rem;
  }

  label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
  }

  input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ccc;
    border-radius: 4px;
  }

  button {
    padding: 0.5rem 1rem;
    background-color: #00adef;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }

  button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }

  #status {
    margin-top: 1rem;
    color: #666;
  }
</style>

<div class="form-group">
  <label for="ip">IP Address</label>
  <input
    type="text"
    id="ip"
    value="*************"
    placeholder="e.g., *************"
    oninput="updateFormData('ip', this.value)"
  />
</div>



<div class="form-group">
  <label for="password">Password</label>
  <input
    type="password"
    id="password"
    value="pcs2ass2ADM"
    placeholder="Router password"
    oninput="updateFormData('password', this.value)"
  />
</div>

<button id="submit-button" onclick="onSubmit()">Connect</button>

<p id="status"></p>
