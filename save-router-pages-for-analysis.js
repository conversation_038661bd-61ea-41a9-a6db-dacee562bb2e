#!/usr/bin/env node

/**
 * Save router pages for analysis to understand device information structure
 */

const { RouterApiClient } = require('./build/lib/RouterApiClient');
const fs = require('fs');

// Configuration
const ROUTER_CONFIG = {
  ip: '*************',
  username: 'admin',
  password: 'pcs2ass2ADM'
};

async function saveRouterPagesForAnalysis() {
  console.log('=== Saving Router Pages for Analysis ===\n');
  
  let client = null;

  try {
    // Step 1: Create and initialize client
    console.log('Step 1: Creating RouterApiClient...');
    client = new RouterApiClient(ROUTER_CONFIG.ip, ROUTER_CONFIG.username, ROUTER_CONFIG.password);
    
    // Step 2: Initialize connection
    console.log('Step 2: Initializing connection...');
    await client.init();
    console.log('✅ Connection initialized successfully\n');

    // Step 3: Save device-related pages
    console.log('Step 3: Saving device-related pages for analysis...\n');
    
    const pagesToSave = [
      {
        name: 'dhcp',
        url: `/cgi-bin/luci/;stok=${client.token}/admin/network/dhcp`,
        description: 'DHCP leases page'
      },
      {
        name: 'wireless',
        url: `/cgi-bin/luci/;stok=${client.token}/admin/status/wireless`,
        description: 'Wireless status page'
      },
      {
        name: 'overview',
        url: `/cgi-bin/luci/;stok=${client.token}/admin/status/overview`,
        description: 'Status overview page'
      },
      {
        name: 'connections',
        url: `/cgi-bin/luci/;stok=${client.token}/admin/status/connections`,
        description: 'Network connections page'
      },
      {
        name: 'hosts',
        url: `/cgi-bin/luci/;stok=${client.token}/admin/network/hosts`,
        description: 'Network hosts page'
      }
    ];

    for (const page of pagesToSave) {
      try {
        console.log(`📄 Saving ${page.description}...`);
        
        // Access the page using the client's axios instance
        const response = await client.axios.get(page.url);
        
        if (response.status === 200 && response.data) {
          const filename = `router-${page.name}-page.html`;
          fs.writeFileSync(filename, response.data);
          console.log(`✅ Saved ${page.description} to ${filename}`);
          
          // Analyze the content briefly
          const content = response.data;
          const hasDeviceKeywords = [
            'device', 'client', 'MAC', 'IP', 'hostname', 
            'connected', 'wireless', 'ethernet', 'lease', 'dhcp'
          ].some(keyword => content.toLowerCase().includes(keyword.toLowerCase()));
          
          if (hasDeviceKeywords) {
            console.log(`  📋 Contains device-related keywords`);
            
            // Look for table structures
            const tableCount = (content.match(/<table/gi) || []).length;
            const rowCount = (content.match(/<tr/gi) || []).length;
            console.log(`  📊 Tables: ${tableCount}, Rows: ${rowCount}`);
            
            // Look for MAC addresses
            const macMatches = content.match(/[0-9A-Fa-f:]{17}/g) || [];
            console.log(`  🔍 MAC addresses found: ${macMatches.length}`);
            
            // Look for IP addresses
            const ipMatches = content.match(/\d+\.\d+\.\d+\.\d+/g) || [];
            console.log(`  🌐 IP addresses found: ${ipMatches.length}`);
          } else {
            console.log(`  ⚠️  No device-related keywords found`);
          }
        } else {
          console.log(`❌ Failed to load ${page.description} - Status: ${response.status}`);
        }
      } catch (error) {
        console.log(`❌ Error saving ${page.description}:`, error.message);
      }
      
      console.log(''); // Empty line for readability
    }

    console.log('🎉 Page saving completed!');
    console.log('\n📋 ANALYSIS INSTRUCTIONS:');
    console.log('1. Open the saved HTML files in a text editor or browser');
    console.log('2. Look for device information in tables, lists, or JSON data');
    console.log('3. Identify the HTML structure containing device details');
    console.log('4. Update the parsing logic in RouterApiClient accordingly');
    console.log('\n📁 Saved files:');
    console.log('- router-dhcp-page.html (DHCP leases)');
    console.log('- router-wireless-page.html (Wireless clients)');
    console.log('- router-overview-page.html (Status overview)');
    console.log('- router-connections-page.html (Network connections)');
    console.log('- router-hosts-page.html (Network hosts)');

  } catch (error) {
    console.error('❌ Analysis failed:', error.message);
    if (error.stack) {
      console.error('Stack trace:', error.stack);
    }
  } finally {
    // Clean up
    if (client) {
      try {
        client.stopConnectionCheck();
        console.log('\n🧹 Cleaned up client resources');
      } catch (cleanupError) {
        console.log('⚠️  Error during cleanup:', cleanupError.message);
      }
    }
  }
}

// Run the analysis
saveRouterPagesForAnalysis().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
